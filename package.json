{"name": "json-viewer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noCheck && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroui/react": "2.7.8", "@iconify/react": "latest", "@monaco-editor/react": "^4.6.0", "framer-motion": "^11.18.2", "lucide-react": "^0.511.0", "monaco-editor": "^0.47.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-syntax-highlighter": "^15.5.0", "source-map-js": "^1.2.1", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/generator": "^7.27.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.20", "postcss": "8.4.49", "tailwindcss": "3.4.17", "typescript": "5.7.3", "vite": "^6.0.11"}}