import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useDis<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from "@heroui/react";
import { Icon } from "@iconify/react";
import { AIScriptGenerator } from "./ai-script-generator";
import Editor from "@monaco-editor/react";
import { motion } from "framer-motion";

interface ScriptPanelProps {
  initialJson: string;
  onJsonUpdate: (json: string) => void;
}

export const ScriptPanel: React.FC<ScriptPanelProps> = ({ initialJson, onJsonUpdate }) => {
  const [jsonObject, setJsonObject] = React.useState<any>(null);
  
  React.useEffect(() => {
    try {
      setJsonObject(JSON.parse(initialJson));
    } catch (e) {
      console.error("Invalid JSON", e);
    }
  }, [initialJson]);
  const [script, setScript] = React.useState<string>("// Access the JSON data via the 'json' variable\n// Example: return json.users.filter(u => u.active);\n\nreturn json;");
  const [result, setResult] = React.useState<any>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [isRunning, setIsRunning] = React.useState(false);
  const [savedScripts, setSavedScripts] = React.useState<{ name: string; script: string }[]>([]);
  const [scriptName, setScriptName] = React.useState<string>("");
  const [isSaving, setIsSaving] = React.useState<boolean>(false);
  const { isOpen: isAIModalOpen, onOpen: onAIModalOpen, onClose: onAIModalClose } = useDisclosure();

  // Default scripts that will be added if no saved scripts exist
  const defaultScripts = [
    {
      name: "Extract Keys",
      script: "// Extract all unique keys from the JSON object\nfunction getAllKeys(obj) {\n  if (typeof obj !== 'object' || obj === null) return [];\n  return Object.entries(obj).flatMap(([key, value]) => {\n    if (typeof value === 'object' && value !== null) {\n      return [key, ...getAllKeys(value)];\n    }\n    return key;\n  });\n}\n\nreturn [...new Set(getAllKeys(json))];"
    },
    {
      name: "Flatten Object",
      script: "// Flatten a nested object\nfunction flattenObject(obj, parentKey = '') {\n  return Object.keys(obj).reduce((acc, key) => {\n    const newKey = parentKey ? `${parentKey}.${key}` : key;\n    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {\n      Object.assign(acc, flattenObject(obj[key], newKey));\n    } else {\n      acc[newKey] = obj[key];\n    }\n    return acc;\n  }, {});\n}\n\nreturn Array.isArray(json) ? json.map(item => flattenObject(item)) : flattenObject(json);"
    },
    {
      name: "Find by Value",
      script: "// Find objects containing a specific value\nfunction findInObject(obj, searchValue) {\n  if (typeof obj !== 'object' || obj === null) return [];\n  \n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value === searchValue || \n        (typeof value === 'string' && value.includes(searchValue)) ||\n        (Array.isArray(value) && value.includes(searchValue))) {\n      acc.push({ key, value });\n    }\n    \n    if (typeof value === 'object' && value !== null) {\n      acc.push(...findInObject(value, searchValue));\n    }\n    \n    return acc;\n  }, []);\n}\n\n// Search for this value (edit as needed)\nconst searchValue = '';\nreturn findInObject(json, searchValue);"
    },
    {
      name: "Remove Null Values",
      script: "// Recursively remove null or empty values from an object\nfunction removeEmpty(obj) {\n  return Object.entries(obj)\n    .filter(([_, v]) => v != null && v !== '' && !(Array.isArray(v) && v.length === 0))\n    .reduce((acc, [k, v]) => ({\n      ...acc,\n      [k]: v === Object(v) && !Array.isArray(v) ? removeEmpty(v) : v\n    }), {});\n}\n\nreturn Array.isArray(json) ? json.map(removeEmpty) : removeEmpty(json);"
    },
    {
      name: "Sort Array by Key",
      script: "// Sort an array of objects by a specific key\n// Change this to the key you want to sort by\nconst sortKey = 'id';\nconst sortOrder = 'asc'; // 'asc' or 'desc'\n\nif (!Array.isArray(json)) {\n  return 'Input must be an array of objects';\n}\n\nreturn [...json].sort((a, b) => {\n  if (a[sortKey] < b[sortKey]) return sortOrder === 'asc' ? -1 : 1;\n  if (a[sortKey] > b[sortKey]) return sortOrder === 'asc' ? 1 : -1;\n  return 0;\n});"
    },
    {
      name: "Count Occurrences",
      script: "// Count occurrences of each value for a specific key\n// Change this to the key you want to count\nconst countKey = 'type';\n\nfunction countByKey(obj, key) {\n  if (typeof obj !== 'object' || obj === null) return {};\n  \n  if (Array.isArray(obj)) {\n    return obj.reduce((acc, item) => {\n      if (item && typeof item === 'object' && key in item) {\n        const value = item[key];\n        acc[value] = (acc[value] || 0) + 1;\n      }\n      return acc;\n    }, {});\n  }\n  \n  // Handle nested objects\n  return Object.values(obj).reduce((acc, value) => {\n    if (typeof value === 'object' && value !== null) {\n      const nestedCount = countByKey(value, key);\n      Object.entries(nestedCount).forEach(([k, v]) => {\n        acc[k] = (acc[k] || 0) + v;\n      });\n    }\n    return acc;\n  }, {});\n}\n\nreturn countByKey(json, countKey);"
    }
  ];

  React.useEffect(() => {
    // Load saved scripts from localStorage
    const loadScripts = () => {
      console.log('Loading scripts from localStorage...');
      try {
        const savedScriptsJson = localStorage.getItem("json-viewer-scripts");
        console.log('Raw saved scripts from localStorage:', savedScriptsJson);
        
        if (savedScriptsJson) {
          try {
            const parsedScripts = JSON.parse(savedScriptsJson);
            console.log('Parsed scripts:', parsedScripts);
            
            // Only update if we actually have scripts
            if (parsedScripts && Array.isArray(parsedScripts) && parsedScripts.length > 0) {
              console.log('Setting saved scripts:', parsedScripts);
              setSavedScripts(parsedScripts);
              return;
            }
          } catch (parseError) {
            console.error('Error parsing saved scripts:', parseError);
            // If parsing fails, we'll fall through to set default scripts
          }
        }
        
        // If we get here, either there were no scripts or there was an error
        console.log('Setting default scripts');
        setSavedScripts(defaultScripts);
        localStorage.setItem("json-viewer-scripts", JSON.stringify(defaultScripts));
      } catch (e) {
        console.error("Failed to load saved scripts", e);
        // On error, still set default scripts
        setSavedScripts(defaultScripts);
        localStorage.setItem("json-viewer-scripts", JSON.stringify(defaultScripts));
      }
    };

    loadScripts();
    
    // Add event listener for storage changes to sync across tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "json-viewer-scripts") {
        console.log('Storage changed, reloading scripts');
        loadScripts();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const handleScriptChange = (value: string | undefined) => {
    if (value !== undefined) {
      setScript(value);
    }
  };

  const runScript = () => {
    setIsRunning(true);
    setError(null);
    
    try {
      // Create a safe function from the script
      const scriptFunction = new Function("json", script);
      
      // Execute the function with the JSON data
      const scriptResult = scriptFunction(jsonObject);
      
      // Update the result without modifying the original JSON
      setResult(scriptResult);
      
      // Don't update the parent component's JSON to keep other tabs unchanged
      // The result will be displayed in the result panel only
    } catch (err) {
      setError((err as Error).message);
      setResult(null);
    } finally {
      setIsRunning(false);
    }
  };

  const saveScript = () => {
    if (!scriptName.trim()) {
      setError("Please enter a script name");
      return;
    }
    
    setIsSaving(true);
    try {
      const newScript = {
        name: scriptName.trim(),
        script,
      };
      
      console.log('Saving script:', newScript);
      
      const updatedScripts = [...savedScripts, newScript];
      console.log('Updated scripts:', updatedScripts);
      
      localStorage.setItem("json-viewer-scripts", JSON.stringify(updatedScripts));
      
      // Verify the save
      const saved = localStorage.getItem("json-viewer-scripts");
      console.log('Verified saved scripts:', saved);
      
      setSavedScripts(updatedScripts);
      setScriptName("");
      setIsSaving(false);
    } catch (err) {
      console.error('Error saving script:', err);
      setError("Failed to save script: " + (err as Error).message);
      setIsSaving(false);
    }
  };

  const loadScript = (script: string) => {
    setScript(script);
  };

  const deleteScript = (index: number) => {
    const updatedScripts = [...savedScripts];
    updatedScripts.splice(index, 1);
    
    try {
      localStorage.setItem("json-viewer-scripts", JSON.stringify(updatedScripts));
      setSavedScripts(updatedScripts);
    } catch (err) {
      setError("Failed to delete script");
    }
  };

  return (
    <div className="p-4">
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex-1">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold">Script Editor</h3>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Button
                  color="primary"
                  variant="flat"
                  onPress={onAIModalOpen}
                  startContent={<Icon icon="lucide:sparkles" />}
                  className="bg-gradient-to-r from-purple-500 to-blue-500 text-white"
                >
                  Generate with AI
                </Button>
                <Button
                  color="primary"
                  onPress={runScript}
                  isLoading={isRunning}
                  startContent={!isRunning && <Icon icon="lucide:play" />}
                >
                  Run Script
                </Button>
              </div>
            </div>
          </div>
          
          <Card className="w-full">
            <CardBody className="p-0">
              <div className="h-[400px] overflow-hidden rounded-medium border border-divider">
                <Editor
                  height="100%"
                  defaultLanguage="javascript"
                  value={script}
                  onChange={handleScriptChange}
                  options={{
                    minimap: { enabled: false },
                    fontSize: 14,
                    wordWrap: "on",
                    scrollBeyondLastLine: true,
                    scrollbar: {
                      vertical: 'auto',
                      horizontal: 'auto',
                      useShadows: true,
                      verticalScrollbarSize: 8,
                      horizontalScrollbarSize: 8
                    },
                    automaticLayout: true,
                    lineNumbers: 'on',
                    renderLineHighlight: 'all',
                    scrollBeyondLastLine: false,
                    folding: true,
                    lineDecorationsWidth: 20,
                    tabSize: 2,
                  }}
                  loading={
                    <div className="flex items-center justify-center h-full">
                      <Spinner color="primary" />
                    </div>
                  }
                  theme={document.documentElement.classList.contains("dark") ? "vs-dark" : "light"}
                />
              </div>
            </CardBody>
          </Card>
          
          <div className="flex items-center gap-2 mt-4">
            <input
              type="text"
              value={scriptName}
              onChange={(e) => setScriptName(e.target.value)}
              placeholder="Script name"
              className="flex-1 px-3 py-2 rounded-medium border border-divider bg-content1 text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <Button
              color="primary"
              variant="flat"
              onPress={saveScript}
              isLoading={isSaving}
            >
              Save Script
            </Button>
          </div>
          
          {error && (
            <div className="mt-4 p-3 bg-danger-50 dark:bg-danger-900/20 text-danger-600 dark:text-danger-300 rounded-medium">
              {error}
            </div>
          )}
        </div>
        
        <div className="lg:w-1/3">
          <div className="mb-2">
            <h3 className="text-lg font-semibold">Result</h3>
          </div>
          
          <Card className="mb-4">
            <CardBody className="p-4 max-h-[300px] overflow-auto">
              {isRunning ? (
                <div className="flex items-center justify-center p-4">
                  <Spinner color="primary" />
                </div>
              ) : (
                <pre className="whitespace-pre-wrap break-words text-sm">
                  {result === null ? 'No result yet. Run your script to see the output.' : 
                   typeof result === 'string' ? result : 
                   JSON.stringify(result, null, 2)}
                </pre>
              )}
            </CardBody>
          </Card>
          
          <div className="mb-2">
            <h3 className="text-lg font-semibold">Saved Scripts</h3>
          </div>
          
          <Card>
            <CardBody className="p-0">
              {savedScripts.length === 0 ? (
                <div className="p-4 text-center text-default-400">
                  No saved scripts yet
                </div>
              ) : (
                <ul className="divide-y divide-divider">
                  {savedScripts.map((savedScript, index) => (
                    <li key={index} className="p-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{savedScript.name}</span>
                        <div className="flex gap-2">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => loadScript(savedScript.script)}
                          >
                            <Icon icon="mdi:content-copy" className="text-lg" />
                          </Button>
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="danger"
                            onPress={() => deleteScript(index)}
                          >
                            <Icon icon="mdi:delete" className="text-lg" />
                          </Button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
      
      <AIScriptGenerator
        isOpen={isAIModalOpen}
        onClose={onAIModalClose}
        onScriptGenerated={(generatedScript) => {
          setScript(generatedScript);
          onAIModalClose();
        }}
        jsonData={jsonObject}
      />
    </div>
  );
};
