import React from 'react';
import { Navbar, Nav<PERSON><PERSON><PERSON>, Nav<PERSON><PERSON><PERSON><PERSON>, Button, Tooltip, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { Icon } from '@iconify/react';
import { ThemeSwitcher } from './theme-switcher';

interface HeaderProps {
  addNewTab: () => void;
}

export const Header: React.FC<HeaderProps> = ({ addNewTab }) => {
  return (
    <Navbar maxWidth="full" className="border-b border-divider">
      <NavbarBrand>
        <div className="flex items-center gap-2">
          <Icon icon="lucide:braces" className="text-primary text-xl" />
          <p className="font-bold text-inherit">JSON Editor</p>
        </div>
      </NavbarBrand>

      <NavbarContent justify="end" className="gap-4">
        <ThemeSwitcher />
        
        {/* <Tooltip content="Add new tab" placement="bottom">
          <Button 
            isIconOnly 
            variant="light" 
            onPress={addNewTab}
            aria-label="Add new tab"
          >
            <Icon icon="lucide:plus" className="text-lg" />
          </Button>
        </Tooltip> */}
        
        {/* <Dropdown>
          <DropdownTrigger>
            <Button 
              variant="light" 
              isIconOnly
              aria-label="More options"
            >
              <Icon icon="lucide:more-vertical" className="text-lg" />
            </Button>
          </DropdownTrigger>
          <DropdownMenu aria-label="Actions">
            <DropdownItem key="help" startContent={<Icon icon="lucide:help-circle" />}>
              Help
            </DropdownItem>
            <DropdownItem key="about" startContent={<Icon icon="lucide:info" />}>
              About
            </DropdownItem>
            <DropdownItem key="github" startContent={<Icon icon="lucide:github" />}>
              GitHub
            </DropdownItem>
          </DropdownMenu>
        </Dropdown> */}
      </NavbarContent>
    </Navbar>
  );
};