import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";

type JsonType = string | number | boolean | null | JsonType[] | { [key: string]: JsonType };

interface JsonSummaryProps {
  isOpen: boolean;
  onClose: () => void;
  jsonObject: JsonType;
  jsonString: string;
}

export const JsonSummary: React.FC<JsonSummaryProps> = ({ isOpen, onClose, jsonObject, jsonString }) => {
  const getJsonDepth = (obj: JsonType): number => {
    if (obj === null || typeof obj !== 'object') return 0;
    
    let maxDepth = 0;
    
    Object.values(obj).forEach((value) => {
      if (value !== null && typeof value === 'object') {
        const depth = getJsonDepth(value) + 1;
        maxDepth = Math.max(maxDepth, depth);
      }
    });
    
    return max<PERSON>epth;
  };
  
  const countKeys = (obj: JsonType): number => {
    if (obj === null || typeof obj !== 'object') return 0;
    
    let count = Object.keys(obj).length;
    
    Object.values(obj).forEach((value) => {
      if (value !== null && typeof value === 'object') {
        count += countKeys(value);
      }
    });
    
    return count;
  };

  const countValues = (obj: JsonType): number => {
    if (obj === null || typeof obj !== 'object') return 1;
    
    let count = 0;
    Object.values(obj).forEach(value => {
      if (value !== null && typeof value === 'object') {
        count += countValues(value);
      } else {
        count += 1;
      }
    });
    return count;
  };

  const getTypeCounts = (): Record<string, number> => {
    const typeCounts: Record<string, number> = {};

    const countTypes = (item: JsonType) => {
      if (item === null) {
        typeCounts['null'] = (typeCounts['null'] || 0) + 1;
        return;
      }
      
      const type = Array.isArray(item) ? 'array' : typeof item;
      typeCounts[type] = (typeCounts[type] || 0) + 1;

      if (type === 'object') {
        Object.values(item as Record<string, JsonType>).forEach(countTypes);
      }
    };

    if (jsonObject !== null && typeof jsonObject === 'object') {
      countTypes(jsonObject);
    }

    return typeCounts;
  };

  const typeCounts = getTypeCounts();
  const totalKeys = countKeys(jsonObject);
  const totalValues = countValues(jsonObject);
  const depth = getJsonDepth(jsonObject);
  const sizeInBytes = new TextEncoder().encode(jsonString).length;
  const sizeInKB = (sizeInBytes / 1024).toFixed(2);
  const lineCount = jsonString.split('\n').length;

  const renderStatCard = (title: string, value: string | number, icon: string) => (
    <div className="flex flex-col items-center p-4 bg-default-50 dark:bg-default-100 rounded-lg">
      <div className="p-2 mb-2 rounded-full bg-primary-50 dark:bg-primary-900/20">
        <Icon icon={icon} className="text-primary-500 text-xl" />
      </div>
      <span className="text-sm font-medium text-default-500">{title}</span>
      <span className="text-lg font-semibold">{value}</span>
    </div>
  );

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:file-text" className="text-primary" />
            <span>JSON Summary</span>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {renderStatCard("Total Keys", totalKeys, "lucide:key")}
              {renderStatCard("Total Values", totalValues, "lucide:hash")}
              {renderStatCard("Depth", depth, "lucide:layers")}
              {renderStatCard("Size", `${sizeInKB} KB`, "lucide:ruler")}
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Type Distribution</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {Object.entries(typeCounts).map(([type, count]) => (
                  <div key={type} className="bg-default-50 dark:bg-default-100 p-3 rounded-lg">
                    <div className="text-sm text-default-500 capitalize">{type}</div>
                    <div className="font-medium">{count}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Quick Stats</h3>
              <div className="bg-default-50 dark:bg-default-100 p-4 rounded-lg">
                <ul className="space-y-2">
                  <li className="flex justify-between">
                    <span className="text-default-500">Lines of JSON:</span>
                    <span className="font-medium">{lineCount}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-default-500">Size in Bytes:</span>
                    <span className="font-medium">{sizeInBytes} bytes</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-default-500">Is Array:</span>
                    <span className="font-medium">{Array.isArray(jsonObject) ? 'Yes' : 'No'}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-default-500">Is Empty:</span>
                    <span className="font-medium">
                      {jsonObject === null || (typeof jsonObject === 'object' && Object.keys(jsonObject).length === 0) ? 'Yes' : 'No'}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
