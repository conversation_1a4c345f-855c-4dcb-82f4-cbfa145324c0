import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
} from "@heroui/react";
import { Icon } from "@iconify/react";

interface JsonInputModalProps {
  isOpen: boolean;
  onClose: () => void;
  onJsonLoaded: (json: string) => void;
}

export const JsonInputModal: React.FC<JsonInputModalProps> = ({
  isOpen = false,
  onClose,
  onJsonLoaded,
}) => {
  console.log('JsonInputModal rendered with isOpen:', isOpen);
  const [activeTab, setActiveTab] = useState<"file" | "url">("file");
  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Reset state when modal is opened
  useEffect(() => {
    if (isOpen) {
      setActiveTab("file");
      setUrl("");
      setError(null);
      setIsLoading(false);
    }
  }, [isOpen]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('File input changed');
    const file = e.target.files?.[0];
    if (!file) {
      console.log('No file selected');
      return;
    }
    console.log('Selected file:', file.name);

    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        onJsonLoaded(content);
        onClose();
      } catch (err) {
        setError("Failed to read file. Please make sure it's a valid JSON file.");
      }
    };
    reader.onerror = () => {
      setError("Error reading file. Please try again.");
    };
    reader.readAsText(file);
  };

  const handleUrlSubmit = async () => {
    console.log('URL submit clicked with URL:', url);
    if (!url.trim()) {
      console.log('URL is empty');
      setError("Please enter a valid URL");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const json = await response.json();
      onJsonLoaded(JSON.stringify(json, null, 2));
      onClose();
    } catch (err) {
      setError("Failed to load JSON from URL. Please check the URL and try again.");
      console.error("Error loading JSON:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (tab: "file" | "url") => {
    setActiveTab(tab);
    setError(null);
  };

  if (!isOpen) {
    console.log('JsonInputModal not rendered because isOpen is false');
    return null;
  }

  console.log('Rendering JsonInputModal with activeTab:', activeTab);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="lg"
      className="z-50"
      aria-labelledby="modal-title"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">Load JSON</ModalHeader>
        <ModalBody>
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={(key) => handleTabChange(key as "file" | "url")}
            aria-label="JSON input options"
            className="mb-4"
          >
            <Tab key="file" title="From File" />
            <Tab key="url" title="From URL" />
          </Tabs>

          <div className="mt-4">
            {activeTab === "file" ? (
              <div className="space-y-4">
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Icon icon="mdi:file-upload" className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                  <p className="text-gray-600">Click to upload or drag and drop</p>
                  <p className="text-sm text-gray-500 mt-1">JSON files only</p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json,application/json"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter JSON URL"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    className="flex-1"
                    onKeyDown={(e) => e.key === 'Enter' && handleUrlSubmit()}
                  />
                  <Button
                    onClick={handleUrlSubmit}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <Spinner size="sm" />
                        Loading...
                      </>
                    ) : (
                      <>
                        <Icon icon="mdi:download" className="w-5 h-5" />
                        Load
                      </>
                    )}
                  </Button>
                </div>
                <p className="text-sm text-gray-500">
                  Enter a public URL that returns valid JSON data
                </p>
              </div>
            )}

            {error && (
              <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
