import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Input, Textarea, Spinner } from "@heroui/react";
import { Icon } from "@iconify/react";

interface AIScriptGeneratorProps {
  isOpen: boolean;
  onClose: () => void;
  onScriptGenerated: (script: string) => void;
  jsonData: any;
}

export const AIScriptGenerator: React.FC<AIScriptGeneratorProps> = ({
  isOpen,
  onClose,
  onScriptGenerated,
  jsonData
}) => {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');

  const generateScript = async () => {
    if (!prompt.trim()) {
      setError('Please enter a description of the script you want to generate');
      return;
    }

    setIsGenerating(true);
    setError('');

    try {
      // In a real implementation, you would call your AI service here
      // For now, we'll use a simple example response
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // This is a mock response - replace with actual AI service call
      const mockResponse = `// Generated script based on: ${prompt}
// This is a placeholder - replace with actual AI-generated code
// The JSON data is available as 'json' variable

try {
  // Example: Filter items based on prompt
  if (Array.isArray(json)) {
    return json.filter(item => 
      JSON.stringify(item).toLowerCase().includes('${prompt.toLowerCase()}')
    );
  }
  
  // Example: Get specific property
  if (typeof json === 'object' && json !== null) {
    return json.${prompt.toLowerCase().split(' ')[0]} || 'Property not found';
  }
  
  return json;
} catch (error) {
  return { error: 'Error processing script', details: error.message };
}`;

      onScriptGenerated(mockResponse);
      onClose();
    } catch (err) {
      setError('Failed to generate script. Please try again.');
      console.error('AI generation error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="lucide:sparkles" className="text-yellow-500" />
            <span>AI Script Generator</span>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <p className="text-sm text-default-500">
              Describe the script you want to generate. For example: "Filter users by active status" or "Calculate total price of all items"
            </p>
            
            <Textarea
              label="Describe your script"
              placeholder="E.g., Create a script that filters active users and sorts them by name"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full"
              minRows={3}
              isDisabled={isGenerating}
            />
            
            {error && (
              <div className="p-3 bg-danger-50 dark:bg-danger-900/20 text-danger-600 dark:text-danger-300 rounded-medium text-sm">
                {error}
              </div>
            )}
            
            <div className="bg-default-50 dark:bg-default-100 p-4 rounded-medium">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 pt-1">
                  <div className="p-1.5 bg-primary-50 dark:bg-primary-900/30 rounded-full">
                    <Icon icon="lucide:lightbulb" className="text-primary-500" />
                  </div>
                </div>
                <div className="text-sm text-default-600 dark:text-default-500">
                  <p className="font-medium text-default-700 dark:text-default-300 mb-1">Tips for better results:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Be specific about what you want to achieve</li>
                    <li>Mention any specific properties or conditions</li>
                    <li>Specify the format of the output you expect</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={onClose} isDisabled={isGenerating}>
            Cancel
          </Button>
          <Button 
            color="primary" 
            onPress={generateScript}
            isDisabled={isGenerating || !prompt.trim()}
            startContent={isGenerating ? null : <Icon icon="lucide:sparkles" />}
          >
            {isGenerating ? (
              <span className="flex items-center gap-2">
                <Spinner size="sm" color="current" />
                Generating...
              </span>
            ) : (
              'Generate Script'
            )}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
