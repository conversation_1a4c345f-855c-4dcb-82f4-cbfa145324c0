import React from "react";

type Theme = "light" | "dark";

export const useTheme = () => {
  const [theme, setThemeState] = React.useState<Theme>(() => {
    // Check if theme is stored in localStorage
    if (typeof window === 'undefined') return 'light';
    
    const savedTheme = localStorage.getItem("theme");
    
    // Check system preference if no saved theme
    if (!savedTheme) {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    
    return (savedTheme as Theme) || "light";
  });

  React.useEffect(() => {
    // Update document class when theme changes
    if (theme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
    
    // Save to localStorage
    localStorage.setItem("theme", theme);
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  return { theme, setTheme };
};
