@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  font-family: 'Inter', sans-serif;
}

.json-tree-container .json-key {
  color: #7c3aed;
}

.json-tree-container .json-string {
  color: #16a34a;
}

.json-tree-container .json-number {
  color: #2563eb;
}

.json-tree-container .json-boolean {
  color: #d97706;
}

.json-tree-container .json-null {
  color: #dc2626;
}

.dark .json-tree-container .json-key {
  color: #a3e635;
}

.dark .json-tree-container .json-string {
  color: #4ade80;
}

.dark .json-tree-container .json-number {
  color: #60a5fa;
}

.dark .json-tree-container .json-boolean {
  color: #fbbf24;
}

.dark .json-tree-container .json-null {
  color: #f87171;
}

.monaco-editor {
  border-radius: 8px;
  overflow: hidden;
}

.glassmorphism {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glassmorphism {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
