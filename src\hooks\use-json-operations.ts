import React from "react";
import { addToast } from "@heroui/react";

interface JsonError {
  message: string;
  position?: number;
  line?: number;
  column?: number;
  expected?: string;
}

interface ValidationResult {
  isValid: boolean;
  error?: JsonError;
}

interface UseJsonOperationsReturn {
  jsonString: string;
  setJsonString: (json: string) => void;
  jsonObject: any;
  isValidJson: boolean;
  validationError: string | null;
  parseError: string;
  detailedError: JsonError | null;
  formatJson: () => void;
  minifyJson: () => void;
  copyToClipboard: (text: string) => void;
  downloadJson: (json: string, filename?: string) => void;
  validateJson: (json: string) => ValidationResult;
  clearJson: () => void;
  getSuggestedFix: () => string | null;
  getErrorHighlightedJson: () => string;
}

export const useJsonOperations = (initialJson: string): UseJsonOperationsReturn => {
  const [jsonString, setJsonString] = React.useState<string>(initialJson);
  const [jsonObject, setJsonObject] = React.useState<any>(null);
  const [isValidJson, setIsValidJson] = React.useState<boolean>(true);
  const [parseError, setParseError] = React.useState<string>("");
  const [validationError, setValidationError] = React.useState<string | null>(null);
  const [detailedError, setDetailedError] = React.useState<JsonError | null>(null);
  
  // Update JSON object when jsonString changes
  React.useEffect(() => {
    try {
      const parsed = JSON.parse(jsonString);
      setJsonObject(parsed);
      setIsValidJson(true);
      setParseError("");
      setDetailedError(null);
      setValidationError(null);
    } catch (error) {
      setIsValidJson(false);
      const errorMessage = (error as Error).message;
      setParseError(errorMessage);
      
      // Extract detailed error information
      const detailedInfo = extractErrorDetails(errorMessage, jsonString);
      setDetailedError(detailedInfo);
    }
  }, [jsonString]);
  
  const extractErrorDetails = (errorMessage: string, jsonStr: string): JsonError => {
    const error: JsonError = { message: errorMessage };
    
    // Try to extract position information from the error message
    const positionMatch = errorMessage.match(/position (\d+)/);
    if (positionMatch && positionMatch[1]) {
      const position = parseInt(positionMatch[1], 10);
      error.position = position;
      
      // Calculate line and column numbers
      const lines = jsonStr.substring(0, position).split('\n');
      error.line = lines.length;
      error.column = lines[lines.length - 1].length + 1;
      
      // Try to determine what was expected
      error.expected = suggestExpectedToken(jsonStr, position);
    }
    
    return error;
  };
  
  const suggestExpectedToken = (jsonStr: string, position: number): string => {
    if (position >= jsonStr.length) return 'end of JSON';
    
    const char = jsonStr[position];
    const prevChar = position > 0 ? jsonStr[position - 1] : '';
    
    // Check what might be expected based on the current character
    switch (char) {
      case '"':
        return 'string value';
      case '{':
        return 'object key';
      case '}':
        return 'comma or end of object';
      case '[':
        return 'array value or closing bracket';
      case ']':
        return 'comma or end of array';
      case ',':
        return 'value';
      case ':':
        return 'value';
      default:
        if (prevChar === '"') return 'colon';
        if (prevChar === ':') return 'value';
        if (prevChar === ',') return 'value';
        if (prevChar === '{') return 'key or closing brace';
        if (prevChar === '[') return 'value or closing bracket';
        return 'valid JSON token';
    }
  };
  
  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonString);
      setJsonString(JSON.stringify(parsed, null, 2));
      addToast({
        title: "Success",
        description: "JSON formatted successfully",
        color: "success",
      });
    } catch (error) {
      addToast({
        title: "Error",
        description: "Invalid JSON",
        color: "danger",
      });
    }
  };
  
  const minifyJson = () => {
    try {
      const parsed = JSON.parse(jsonString);
      setJsonString(JSON.stringify(parsed));
      addToast({
        title: "Success",
        description: "JSON minified successfully",
        color: "success",
      });
    } catch (error) {
      addToast({
        title: "Error",
        description: "Invalid JSON",
        color: "danger",
      });
    }
  };
  
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      addToast({
        title: "Copied",
        description: "Text copied to clipboard",
        color: "success",
      });
    } catch (error) {
      addToast({
        title: "Error",
        description: "Failed to copy to clipboard",
        color: "danger",
      });
    }
  };
  
  const downloadJson = (json: string, filename: string = 'data.json') => {
    try {
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      addToast({
        title: "Download Started",
        description: `Downloading ${filename}`,
        color: "success",
      });
    } catch (error) {
      addToast({
        title: "Error",
        description: "Failed to download file",
        color: "danger",
      });
    }
  };
  
  const getSuggestedFix = (): string | null => {
    if (!detailedError) return null;
    
    const { message, position, expected } = detailedError;
    
    if (message.includes('Unexpected token')) {
      return 'Check for missing commas between array elements or object properties';
    }
    
    if (message.includes('Unexpected end of JSON input')) {
      return 'The JSON seems to be incomplete. Check for missing closing brackets or braces';
    }
    
    if (expected) {
      return `Expected ${expected}`;
    }
    
    if (position !== undefined) {
      return `Error at position ${position}${detailedError.line ? `, line ${detailedError.line}, column ${detailedError.column}` : ''}`;
    }
    
    return null;
  };
  
  const getErrorHighlightedJson = (): string => {
    if (!detailedError || detailedError.position === undefined) return jsonString;
    
    const position = detailedError.position;
    return jsonString.substring(0, position) + 
           '👉' + 
           jsonString.substring(position);
  };
  
  // Validate JSON string with detailed error information
  const validateJson = (json: string): ValidationResult => {
    try {
      JSON.parse(json);
      setValidationError(null);
      setDetailedError(null);
      return { isValid: true };
    } catch (error) {
      const errorMessage = (error as Error).message;
      setValidationError(errorMessage);
      
      // Extract detailed error information
      const match = errorMessage.match(/at position (\d+)/);
      let errorDetails: JsonError = { message: errorMessage };
      
      if (match) {
        const position = parseInt(match[1], 10);
        const lines = json.substring(0, position).split('\n');
        const line = lines.length;
        const column = lines[lines.length - 1].length + 1;
        
        errorDetails = {
          ...errorDetails,
          position,
          line,
          column
        };
        
        // Try to suggest expected token
        const nextChar = json[position];
        if (nextChar) {
          errorDetails.expected = suggestExpectedToken(json, position);
        }
      }
      
      setDetailedError(errorDetails);
      return { 
        isValid: false, 
        error: errorDetails 
      };
    }
  };
  
  const clearJson = () => {
    setJsonString("");
    setJsonObject(null);
    setValidationError(null);
    setParseError("");
    setDetailedError(null);
  };
  
  return {
    jsonString,
    setJsonString,
    jsonObject,
    isValidJson,
    validationError,
    parseError,
    detailedError,
    formatJson,
    minifyJson,
    copyToClipboard,
    downloadJson,
    validateJson,
    clearJson,
    getSuggestedFix,
    getErrorHighlightedJson
  };
};
