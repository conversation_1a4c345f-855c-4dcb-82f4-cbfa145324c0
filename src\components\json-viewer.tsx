import React from "react";
import { Card, CardBody, Button, Tabs, Tab, Input, Tooltip, addToast, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import { JsonEditor } from "./json-editor";
import { JsonInputModal } from "./json-input-modal";
import { JsonTree } from "./json-tree";
import { ScriptPanel } from "./script-panel";
import { useJsonOperations } from "../hooks/use-json-operations";
import { Icon } from "@iconify/react";

interface JsonViewerProps {
  initialJson: string;
  onJsonChange: (json: string) => void;
  onTitleChange?: (title: string) => void;
}

export const JsonViewer: React.FC<JsonViewerProps> = ({ 
  initialJson, 
  onJsonChange,
  onTitleChange = () => {}
}) => {
  const [viewMode, setViewMode] = React.useState<"tree" | "code" | "script">("code");
  const [isEditable, setIsEditable] = React.useState(true); // Set to true by default
  const [searchTerm, setSearchTerm] = React.useState("");
  const [validationError, setValidationError] = React.useState<string | null>(null);
  const [isInputModalOpen, setIsInputModalOpen] = React.useState(false);
  const [inputModalTab, setInputModalTab] = React.useState<"file" | "url">("file");
  const { isOpen: isClearModalOpen, onOpen: onOpenClearModal, onClose: onCloseClearModal } = useDisclosure();
  
  const { 
    jsonString, 
    setJsonString, 
    jsonObject, 
    isValidJson,
    formatJson,
    minifyJson,
    copyToClipboard,
    downloadJson,
    validateJson,
    clearJson
  } = useJsonOperations(initialJson);
  
  // Handle download with default filename
  const handleDownload = () => {
    try {
      const title = jsonObject && typeof jsonObject === 'object' && 'title' in jsonObject 
        ? String(jsonObject.title) 
        : 'data';
      downloadJson(jsonString, `${title}.json`);
    } catch (error) {
      downloadJson(jsonString, 'data.json');
    }
  };

  React.useEffect(() => {
    if (jsonString !== initialJson) {
      onJsonChange(jsonString);
    }
  }, [jsonString, onJsonChange, initialJson]);

  const handleJsonChange = (newJson: string) => {
    setJsonString(newJson);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleClearJson = () => {
    onOpenClearModal();
  };

  const confirmClearJson = () => {
    clearJson();
    setSearchTerm("");
    onCloseClearModal();
    addToast({
      title: "Cleared",
      description: "JSON has been cleared successfully",
      color: "success",
    });
  };

  const handleJsonLoaded = React.useCallback((json: string) => {
    console.log("JSON loaded:", json);
    setJsonString(json);
    addToast({
      title: "Success",
      description: "JSON loaded successfully",
      color: "success",
    });
    setIsInputModalOpen(false);
  }, [setJsonString, addToast]);

  const handleValidateJson = () => {
    const { isValid, error } = validateJson(jsonString);
    
    if (isValid) {
      addToast({
        title: "JSON is Valid",
        description: "Your JSON is well-formed",
        color: "success",
      });
      setValidationError(null);
    } else {
      const errorMessage = error?.message || "Invalid JSON syntax";
      const position = error?.position ? ` at position ${error.position}` : '';
      const line = error?.line ? `, line ${error.line}` : '';
      const column = error?.column ? `, column ${error.column}` : '';
      
      setValidationError(`${errorMessage}${position}${line}${column}`);
      
      addToast({
        title: "Invalid JSON",
        description: `Error: ${errorMessage}${position}${line}${column}`,
        color: "danger",
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="w-full h-full"
    >
      <Card className="h-full overflow-hidden">
       
        <CardBody className="p-0 flex flex-col h-full">
          {/* Toolbar */}
          <div className="p-3 border-b border-divider flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Tabs 
                aria-label="View mode" 
                selectedKey={viewMode} 
                onSelectionChange={(key) => setViewMode(key as "tree" | "code" | "script")}
                className="w-full"
                color="primary"
                radius="full"
                size="sm"
              >
                <Tab key="code" title="Code View" />
                <Tab key="tree" title="Tree View" />
                <Tab key="script" title="Script" />
              </Tabs>
              
              <Tooltip content={isEditable ? "Lock editing" : "Edit JSON"}>
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={() => setIsEditable(!isEditable)}
                >
                  <Icon icon={isEditable ? "mdi:lock" : "mdi:pencil"} className="text-lg" />
                </Button>
              </Tooltip>
              
              <Tooltip content="Validate JSON">
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={handleValidateJson}
                >
                  <Icon icon="mdi:check-circle" className="text-lg" />
                </Button>
              </Tooltip>
              
              <Tooltip content="Clear JSON">
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={handleClearJson}
                >
                  <Icon icon="mdi:eraser" className="text-lg" />
                </Button>
              </Tooltip>
            </div>
            
            <div className="flex items-center gap-2">
              <Input
                type="text"
                placeholder="Search in JSON..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-48"
                size="sm"
                startContent={
                  <Icon icon="mdi:magnify" className="text-lg text-default-400" />
                }
              />
              
              <div className="flex items-center space-x-2">
                <Dropdown>
                  <DropdownTrigger>
                    <Button size="sm" variant="solid" color="primary" className="flex items-center gap-1">
                      <Icon icon="mdi:plus" className="w-4 h-4" />
                      <span>Load JSON</span>
                      <Icon icon="mdi:chevron-down" className="w-4 h-4" />
                    </Button>
                  </DropdownTrigger>
                  <DropdownMenu>
                    <DropdownItem 
                      key="file" 
                      onPress={() => {
                        console.log("From File clicked");
                        setInputModalTab("file");
                        setIsInputModalOpen(true);
                        console.log("isInputModalOpen should be true now");
                      }}
                      startContent={<Icon icon="mdi:file-upload" className="w-4 h-4" />}
                    >
                      From File
                    </DropdownItem>
                    <DropdownItem 
                      key="url" 
                      onPress={() => {
                        console.log("From URL clicked");
                        setInputModalTab("url");
                        setIsInputModalOpen(true);
                        console.log("isInputModalOpen should be true now");
                      }}
                      startContent={<Icon icon="mdi:link" className="w-4 h-4" />}
                    >
                      From URL
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
                <Tooltip content="Format JSON">
                  <Button size="sm" variant="ghost" onClick={formatJson}>
                    <Icon icon="mdi:format-indent-increase" className="w-5 h-5" />
                  </Button>
                </Tooltip>
                <Tooltip content="Minify JSON">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={minifyJson}
                    isDisabled={!isValidJson}
                  >
                    <Icon icon="mdi:code-brackets" className="text-lg" />
                  </Button>
                </Tooltip>
                <Tooltip content="Copy to clipboard">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={() => copyToClipboard(jsonString)}
                    isDisabled={!isValidJson}
                  >
                    <Icon icon="mdi:content-copy" className="text-lg" />
                  </Button>
                </Tooltip>
                <Tooltip content="Download JSON">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onPress={handleDownload}
                    isDisabled={!isValidJson}
                  >
                    <Icon icon="mdi:download" className="text-lg" />
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
          
          {/* Content */}
          {validationError && (
            <div className="px-4 pt-2">
              <div className="p-3 bg-danger-50 dark:bg-danger-900/20 text-danger-600 dark:text-danger-300 rounded-medium">
                <div className="flex items-start gap-2">
                  <Icon icon="lucide:alert-triangle" className="text-lg mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Validation Error</p>
                    <p className="text-sm">{validationError}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className="flex-1 overflow-auto">
            {viewMode === 'tree' ? (
              <div className="h-full overflow-auto">
                {isValidJson ? (
                  <JsonTree 
                    data={jsonObject} 
                    searchTerm={searchTerm} 
                    isEditable={isEditable}
                    onEdit={handleJsonChange}
                  />
                ) : (
                  <div className="text-danger p-4 bg-danger-50 dark:bg-danger-900/20 rounded-md">
                    <p className="font-medium">Invalid JSON</p>
                    <p className="text-sm mt-1">Please check your JSON syntax</p>
                  </div>
                )}
              </div>
            ) : viewMode === 'code' ? (
              <div className="h-full">
                <JsonEditor
                  value={jsonString}
                  onChange={handleJsonChange}
                  readOnly={!isEditable}
                />
              </div>
            ) : (
              <div className="h-full">
                <ScriptPanel 
                  initialJson={jsonString}
                  onJsonUpdate={handleJsonChange}
                />
              </div>
            )}
          </div>
        </CardBody>
      </Card>
      <JsonInputModal
        isOpen={isInputModalOpen}
        onClose={() => setIsInputModalOpen(false)}
        onJsonLoaded={handleJsonLoaded}
      />

      <Modal isOpen={isClearModalOpen} onClose={onCloseClearModal}>
        <ModalContent>
          <ModalHeader>Confirm Clear</ModalHeader>
          <ModalBody>
            Are you sure you want to clear the JSON data? This action cannot be undone.
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onCloseClearModal}>
              Cancel
            </Button>
            <Button color="danger" onPress={confirmClearJson}>
              Clear
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </motion.div>
  );
};
