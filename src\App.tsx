import React from "react";
import { Tabs, Tab, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Tooltip } from "@heroui/react";
import { Icon } from "@iconify/react";

import { ThemeSwitcher } from "./components/theme-switcher";
import { JsonViewer } from "./components/json-viewer";
import { Header } from "./components/header";

function App() {
  const [activeTab, setActiveTab] = React.useState<string>("tab1");
  const [tabs, setTabs] = React.useState<{ id: string; title: string; content: string }[]>([
    {
      id: "tab1",
      title: "JSON 1",
      content: JSON.stringify(
        { message: "Welcome to JSON Viewer! Paste or upload your JSON here." },
        null,
        2
      ),
    },
  ]);

  const addNewTab = () => {
    const newTabId = `tab${tabs.length + 1}`;
    setTabs([...tabs, { id: newTabId, title: `JSON ${tabs.length + 1}`, content: "{}" }]);
    setActiveTab(newTabId);
  };

  const updateTabContent = (tabId: string, content: string) => {
    setTabs(tabs.map((tab) => (tab.id === tabId ? { ...tab, content } : tab)));
  };

  const updateTabTitle = (tabId: string, title: string) => {
    setTabs(tabs.map((tab) => (tab.id === tabId ? { ...tab, title } : tab)));
  };

  const closeTab = (tabId: string) => {
    if (tabs.length > 1) {
      const newTabs = tabs.filter((tab) => tab.id !== tabId);
      setTabs(newTabs);
      if (activeTab === tabId) {
        setActiveTab(newTabs[0].id);
      }
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      <Header addNewTab={addNewTab} />

      <main className="flex-1 container mx-auto px-4 py-6 max-w-[1400px]">
        <div className="relative">
          <Tabs
            aria-label="JSON Tabs"
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            className="w-full"
            classNames={{
              tabList:
                "flex gap-1 border-b border-border overflow-x-auto scrollbar-hide",
              tab:
                "relative px-4 py-2 rounded-t-lg text-sm font-medium transition-colors bg-muted/30 text-muted-foreground hover:bg-muted/50 data-[selected=true]:bg-background data-[selected=true]:text-foreground border border-transparent data-[selected=true]:border-border",
              cursor: "", // optional underline
            }}
          >
            {tabs.map((tab) => (
              <Tab
                key={tab.id}
                title={
                  <div className="flex items-center gap-2 group">
                    <span className="whitespace-nowrap">{tab.title}</span>
                    {tabs.length > 1 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          closeTab(tab.id);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity text-default-500 hover:text-danger rounded-full p-1 hover:bg-danger-100 dark:hover:bg-danger-900/20"
                      >
                        <span className="sr-only">Close tab</span>
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M18 6L6 18M6 6L18 18"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                }
              >
                <JsonViewer
                  initialJson={tab.content}
                  onJsonChange={(content) => updateTabContent(tab.id, content)}
                  onTitleChange={(title) => updateTabTitle(tab.id, title)}
                />
              </Tab>
            ))}

            {/* Add Tab styled like a tab */}
            <Tab
              key="add-tab"
              title={
                <Tooltip content="Add new tab" placement="bottom">
                  <Button
                    isIconOnly
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      addNewTab();
                    }}
                    aria-label="Add new tab"
                    className="rounded-t-lg border border-transparent hover:bg-muted/40 text-muted-foreground"
                  >
                    <Icon icon="lucide:plus" className="text-lg" />
                  </Button>
                </Tooltip>
              }
              isDisabled
            />
          </Tabs>
        </div>
      </main>

      <footer className="py-4 px-6 border-t border-divider text-center text-sm text-default-500">
        <p>JSON Viewer - Built by Usman</p>
      </footer>

      <ToastProvider />
    </div>
  );
}

export default App;
