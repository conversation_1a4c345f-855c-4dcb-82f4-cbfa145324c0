import React from "react";
import { Icon } from "@iconify/react";
import { motion, AnimatePresence } from "framer-motion";
import { Button, Input } from "@heroui/react";

interface JsonTreeProps {
  data: any;
  isEditable: boolean;
  searchTerm: string;
  onEdit: (json: string) => void;
}

export const JsonTree: React.FC<JsonTreeProps> = ({ 
  data, 
  isEditable, 
  searchTerm,
  onEdit
}) => {
  const [expandedPaths, setExpandedPaths] = React.useState<Set<string>>(new Set());
  const [editingPath, setEditingPath] = React.useState<string | null>(null);
  const [editValue, setEditValue] = React.useState<string>("");
  
  // Expand all nodes initially
  React.useEffect(() => {
    if (data) {
      const paths = new Set<string>();
      const collectPaths = (obj: any, path: string = "") => {
        if (obj && typeof obj === "object") {
          paths.add(path);
          Object.keys(obj).forEach(key => {
            collectPaths(obj[key], path ? `${path}.${key}` : key);
          });
        }
      };
      collectPaths(data);
      setExpandedPaths(paths);
    }
  }, [data]);
  
  const toggleExpand = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };
  
  const expandAll = () => {
    const paths = new Set<string>();
    const collectPaths = (obj: any, path: string = "") => {
      if (obj && typeof obj === "object") {
        paths.add(path);
        Object.keys(obj).forEach(key => {
          collectPaths(obj[key], path ? `${path}.${key}` : key);
        });
      }
    };
    collectPaths(data);
    setExpandedPaths(paths);
  };
  
  const collapseAll = () => {
    setExpandedPaths(new Set());
  };
  
  const startEditing = (path: string, value: any) => {
    if (!isEditable) return;
    setEditingPath(path);
    setEditValue(typeof value === "object" ? JSON.stringify(value) : String(value));
  };
  
  const saveEdit = (path: string) => {
    if (!path || editingPath !== path) return;
    
    try {
      // Parse the path to navigate to the correct location in the data
      const pathParts = path.split(".");
      const lastPart = pathParts.pop();
      
      if (!lastPart) return;
      
      // Create a deep copy of the data
      const newData = JSON.parse(JSON.stringify(data));
      
      // Navigate to the parent object
      let current = newData;
      pathParts.forEach(part => {
        current = current[part];
      });
      
      // Try to parse the value if it's a valid JSON
      try {
        current[lastPart] = JSON.parse(editValue);
      } catch {
        // If not valid JSON, use the raw string
        current[lastPart] = editValue;
      }
      
      // Update the data
      onEdit(JSON.stringify(newData, null, 2));
      setEditingPath(null);
    } catch (error) {
      console.error("Error saving edit:", error);
    }
  };
  
  const cancelEdit = () => {
    setEditingPath(null);
  };
  
  const isMatch = (key: string, value: any): boolean => {
    if (!searchTerm) return false;
    
    const searchLower = searchTerm.toLowerCase();
    const stringValue = typeof value === "object" 
      ? JSON.stringify(value) 
      : String(value);
    
    return key.toLowerCase().includes(searchLower) || 
           stringValue.toLowerCase().includes(searchLower);
  };
  
  // Highlight all matches for search term
  const highlightMatches = () => {
    if (!searchTerm) return;
    
    // Expand all paths that contain matches
    const paths = new Set<string>();
    const collectMatchingPaths = (obj: any, path: string = "") => {
      if (obj && typeof obj === "object") {
        Object.entries(obj).forEach(([key, value]) => {
          const currentPath = path ? `${path}.${key}` : key;
          if (isMatch(key, value)) {
            // Add all parent paths to ensure they're expanded
            let parentPath = path;
            while (parentPath) {
              paths.add(parentPath);
              const lastDot = parentPath.lastIndexOf('.');
              parentPath = lastDot > 0 ? parentPath.substring(0, lastDot) : '';
            }
            paths.add(currentPath);
          }
          if (typeof value === "object" && value !== null) {
            collectMatchingPaths(value, currentPath);
          }
        });
      }
    };
    
    collectMatchingPaths(data);
    setExpandedPaths(prev => {
      const newPaths = new Set(prev);
      paths.forEach(path => newPaths.add(path));
      return newPaths;
    });
  };
  
  // Run search when searchTerm changes
  React.useEffect(() => {
    highlightMatches();
  }, [searchTerm]);
  
  const renderTreeNode = (key: string, value: any, path: string = key, depth: number = 0): JSX.Element => {
    const isObject = value !== null && typeof value === "object";
    const isExpanded = expandedPaths.has(path);
    const isHighlighted = isMatch(key, value);
    
    const getValueElement = () => {
      if (value === null) return <span className="json-null">null</span>;
      if (typeof value === "boolean") return <span className="json-boolean">{String(value)}</span>;
      if (typeof value === "number") return <span className="json-number">{value}</span>;
      if (typeof value === "string") {
        // Check if it's a URL to an image
        if (/^https?:\/\/.*\.(jpg|jpeg|png|gif|webp)$/i.test(value)) {
          return (
            <div className="flex flex-col">
              <span className="json-string">"{value}"</span>
              <img 
                src={value} 
                alt="Preview" 
                className="mt-2 max-w-xs max-h-40 rounded-medium object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          );
        }
        return <span className="json-string">"{value}"</span>;
      }
      return <span>{String(value)}</span>;
    };
    
    return (
      <div 
        key={path} 
        className={`pl-${depth * 4} ${isHighlighted ? 'bg-primary-100 dark:bg-primary-900/20' : ''}`}
      >
        <div className="flex items-start py-1">
          {isObject ? (
            <button
              onClick={() => toggleExpand(path)}
              className="mr-1 p-1 hover:bg-default-100 dark:hover:bg-default-800 rounded-full"
            >
              <Icon 
                icon={isExpanded ? "lucide:chevron-down" : "lucide:chevron-right"} 
                className="text-default-500" 
                width="16" 
              />
            </button>
          ) : (
            <span className="w-6"></span>
          )}
          
          <div className="flex-1">
            <span 
              className={`json-key font-medium cursor-pointer ${isEditable ? 'hover:underline' : ''}`}
              onClick={() => isEditable && startEditing(path, value)}
            >
              {key}
            </span>
            
            {!isObject && (
              <>
                <span className="mx-1">:</span>
                {editingPath === path ? (
                  <div className="flex items-center gap-2 mt-1">
                    <Input
                      value={editValue}
                      onChange={(e) => setEditValue(e.target.value)}
                      size="sm"
                      className="flex-1"
                      autoFocus
                    />
                    <Button
                      size="sm"
                      color="primary"
                      isIconOnly
                      onPress={() => saveEdit(path)}
                    >
                      <Icon icon="lucide:check" width="16" />
                    </Button>
                    <Button
                      size="sm"
                      color="danger"
                      isIconOnly
                      onPress={cancelEdit}
                    >
                      <Icon icon="lucide:x" width="16" />
                    </Button>
                  </div>
                ) : (
                  getValueElement()
                )}
              </>
            )}
            
            {isObject && (
              <span className="ml-2 text-default-400">
                {Array.isArray(value) ? `Array[${value.length}]` : `Object{${Object.keys(value).length}}`}
              </span>
            )}
          </div>
        </div>
        
        {isObject && isExpanded && (
          <AnimatePresence>
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="ml-4 border-l-2 border-divider pl-2"
            >
              {Array.isArray(value) ? (
                value.map((item, index) => 
                  renderTreeNode(String(index), item, `${path}.${index}`, depth + 1)
                )
              ) : (
                Object.entries(value).map(([k, v]) => 
                  renderTreeNode(k, v, `${path}.${k}`, depth + 1)
                )
              )}
            </motion.div>
          </AnimatePresence>
        )}
      </div>
    );
  };
  
  if (!data) {
    return <div className="p-4 text-default-500">No data to display</div>;
  }
  
  return (
    <div className="json-tree-container p-4">
      <div className="flex justify-end gap-2 mb-4">
        <Button
          size="sm"
          variant="flat"
          onPress={expandAll}
          startContent={<Icon icon="lucide:chevrons-down" width="16" />}
        >
          Expand All
        </Button>
        <Button
          size="sm"
          variant="flat"
          onPress={collapseAll}
          startContent={<Icon icon="lucide:chevrons-right" width="16" />}
        >
          Collapse All
        </Button>
      </div>
      
      {typeof data === "object" && data !== null ? (
        Object.entries(data).map(([key, value]) => 
          renderTreeNode(key, value)
        )
      ) : (
        <div>Invalid JSON structure</div>
      )}
    </div>
  );
};